/**
 * 量子共鸣者 - 游戏控制器
 * 统一管理游戏状态、输入处理、游戏循环等核心功能
 */

class GameController {
    constructor() {
        this.gameState = 'menu'; // 'menu', 'playing', 'paused', 'gameOver'
        this.isInitialized = false;
        this.isRunning = false;
        
        // 游戏循环
        this.lastTime = 0;
        this.accumulator = 0;
        this.fixedTimeStep = 1 / 60; // 60 FPS
        this.maxFrameTime = 0.25; // 最大帧时间
        
        // 输入管理
        this.inputManager = null;
        this.currentLevel = null;
        
        // 游戏设置
        this.settings = {
            difficulty: 'normal', // 'easy', 'normal', 'hard'
            enableSound: true,
            enableMusic: true,
            enableParticleEffects: true,
            enableScreenShake: false,
            masterVolume: 0.8,
            musicVolume: 0.7,
            sfxVolume: 0.9
        };
        
        // 事件监听器
        this.eventListeners = new Map();
        
        console.log('🎮 游戏控制器已创建');
    }

    /**
     * 初始化游戏
     */
    async init() {
        try {
            console.log('🎮 开始初始化游戏控制器...');
            console.log('📋 当前初始化状态:', this.isInitialized);

            // 步骤1: 加载设置
            console.log('📋 步骤1: 加载游戏设置...');
            await this.loadSettings();
            console.log('✅ 步骤1完成: 游戏设置加载完成');

            // 步骤2: 初始化存储服务（如果存在）
            console.log('📋 步骤2: 初始化存储服务...');
            if (window.storageService && typeof storageService.init === 'function') {
                try {
                    await storageService.init();
                    console.log('✅ 存储服务初始化完成');
                } catch (error) {
                    console.warn('⚠️ 存储服务初始化失败:', error.message);
                }
            } else {
                console.warn('⚠️ 存储服务不可用，跳过初始化');
            }
            console.log('✅ 步骤2完成');

            // 步骤3: 初始化国际化（如果存在）
            console.log('📋 步骤3: 初始化国际化服务...');
            if (window.i18n && typeof i18n.init === 'function') {
                try {
                    await i18n.init();
                    console.log('✅ 国际化服务初始化完成');
                } catch (error) {
                    console.warn('⚠️ 国际化服务初始化失败:', error.message);
                }
            } else {
                console.warn('⚠️ 国际化服务不可用，跳过初始化');
            }
            console.log('✅ 步骤3完成');

            // 步骤4: 初始化物理引擎（如果存在）
            console.log('📋 步骤4: 初始化物理引擎...');
            if (window.physicsEngine) {
                try {
                    physicsEngine.gravity = { x: 0, y: 0 }; // 无重力环境
                    console.log('✅ 物理引擎配置完成');
                } catch (error) {
                    console.warn('⚠️ 物理引擎配置失败:', error.message);
                }
            } else {
                console.warn('⚠️ 物理引擎不可用，跳过初始化');
            }
            console.log('✅ 步骤4完成');

            // 步骤5: 初始化量子引擎（如果存在）
            console.log('📋 步骤5: 初始化量子引擎...');
            if (window.quantumEngine && typeof quantumEngine.init === 'function') {
                try {
                    quantumEngine.init();
                    this.setupQuantumEngineCallbacks();
                    console.log('✅ 量子引擎初始化完成');
                } catch (error) {
                    console.warn('⚠️ 量子引擎初始化失败:', error.message);
                }
            } else {
                console.warn('⚠️ 量子引擎不可用，跳过初始化');
            }
            console.log('✅ 步骤5完成');

            // 步骤6: 初始化渲染引擎和输入管理器
            console.log('📋 步骤6: 初始化渲染引擎和输入管理器...');
            await this.initializeRenderingAndInput();
            console.log('✅ 步骤6完成');

            // 步骤7: 应用设置
            console.log('📋 步骤7: 应用游戏设置...');
            this.applySettings();
            console.log('✅ 步骤7完成');

            // 步骤8: 设置初始化完成标志
            console.log('📋 步骤8: 设置初始化完成标志...');
            this.isInitialized = true;
            console.log('✅ 游戏控制器初始化完成，isInitialized =', this.isInitialized);

            // 步骤9: 显示主菜单
            console.log('📋 步骤9: 显示主菜单...');
            this.showMainMenu();
            console.log('✅ 步骤9完成');

            console.log('🎉 游戏控制器完整初始化流程完成！');

        } catch (error) {
            console.error('❌ 游戏控制器初始化失败:', error);
            console.error('❌ 错误堆栈:', error.stack);
            this.isInitialized = false; // 确保失败时状态正确
            this.showError('游戏控制器初始化失败: ' + error.message);
            throw error; // 重新抛出错误以便调用者知道初始化失败
        }
    }

    /**
     * 初始化渲染引擎和输入管理器
     */
    async initializeRenderingAndInput() {
        // 查找游戏画布
        const canvas = document.getElementById('game-canvas') || document.getElementById('gameCanvas');

        if (!canvas) {
            console.warn('⚠️ 找不到游戏画布元素，跳过渲染引擎和输入管理器初始化');
            return;
        }

        // 初始化渲染引擎（如果存在）
        if (window.renderEngine && typeof renderEngine.init === 'function') {
            try {
                const success = await renderEngine.init(canvas);
                if (success) {
                    console.log('✅ 渲染引擎初始化完成');
                } else {
                    console.warn('⚠️ 渲染引擎初始化失败');
                }
            } catch (error) {
                console.warn('⚠️ 渲染引擎初始化异常:', error.message);
            }
        } else {
            console.warn('⚠️ 渲染引擎不可用，跳过初始化');
        }

        // 初始化输入管理器（如果存在）
        console.log('🔍 检查 InputManager 类是否可用:', typeof window.InputManager);
        if (window.InputManager) {
            try {
                console.log('🎮 开始创建输入管理器实例...');
                this.inputManager = new InputManager();
                console.log('🎮 输入管理器实例创建成功，开始初始化...');
                this.inputManager.init(canvas);
                this.setupInputHandlers();
                console.log('✅ 输入管理器初始化完成');
            } catch (error) {
                console.error('❌ 输入管理器初始化失败:', error);
                console.error('❌ 错误堆栈:', error.stack);
                this.inputManager = null; // 确保设置为 null
            }
        } else {
            console.warn('⚠️ 输入管理器类不可用，跳过初始化');
            this.inputManager = null; // 确保设置为 null
        }
    }

    /**
     * 设置量子引擎回调
     */
    setupQuantumEngineCallbacks() {
        if (!window.quantumEngine) {
            console.warn('⚠️ 量子引擎不存在，跳过回调设置');
            return;
        }

        try {
            quantumEngine.onParticleActivated = (particle, strength) => {
                this.onParticleActivated(particle, strength);
            };

            quantumEngine.onChainReaction = (chain) => {
                this.onChainReaction(chain);
            };

            quantumEngine.onScoreUpdate = (totalScore, scoreIncrease) => {
                this.onScoreUpdate(totalScore, scoreIncrease);
            };

            quantumEngine.onComboUpdate = (combo) => {
                this.onComboUpdate(combo);
            };

            console.log('✅ 量子引擎回调设置完成');
        } catch (error) {
            console.warn('⚠️ 量子引擎回调设置失败:', error.message);
        }
    }

    /**
     * 设置输入处理器
     */
    setupInputHandlers() {
        // 鼠标/触摸输入
        this.inputManager.onPointerDown = (x, y) => {
            this.handlePointerInput(x, y);
        };
        
        this.inputManager.onPointerMove = (x, y) => {
            this.handlePointerMove(x, y);
        };
        
        // 键盘输入
        this.inputManager.onKeyDown = (key) => {
            this.handleKeyInput(key);
        };
        
        // 频率输入（来自音频输入或滑块）
        this.inputManager.onFrequencyChange = (frequency) => {
            this.handleFrequencyInput(frequency);
        };
    }

    /**
     * 初始化关卡
     * @param {Object} levelConfig - 关卡配置
     */
    async initializeLevel(levelConfig) {
        if (!this.isInitialized) {
            console.error('❌ 游戏未初始化，无法初始化关卡');
            throw new Error('游戏未初始化');
        }

        try {
            console.log('🎮 初始化关卡:', levelConfig.name || levelConfig.id);

            // 确保音频引擎已初始化
            if (window.audioEngine && !audioEngine.isReady()) {
                await audioEngine.init();
            }

            // 重置游戏状态（但不改变游戏状态为menu）
            if (window.physicsEngine) {
                physicsEngine.clear();
            }
            if (window.quantumEngine) {
                quantumEngine.reset();
            }
            this.accumulator = 0;

            // 创建关卡实例
            if (window.Level) {
                this.currentLevel = new Level(levelConfig);
                await this.currentLevel.load();
            } else {
                // 如果Level类不存在，直接使用配置
                this.currentLevel = levelConfig;
            }

            console.log('✅ 关卡初始化完成:', levelConfig.name || levelConfig.id);

        } catch (error) {
            console.error('❌ 关卡初始化失败:', error);
            throw error;
        }
    }

    /**
     * 开始游戏
     * @param {Object} levelConfig - 关卡配置
     */
    async startGame(levelConfig = null) {
        if (!this.isInitialized) {
            console.error('❌ 游戏未初始化');
            return;
        }

        try {
            // 确保音频引擎已初始化
            if (window.audioEngine && !audioEngine.isReady()) {
                await audioEngine.init();
            }

            // 重置游戏状态
            this.resetGameState();

            // 加载关卡
            if (levelConfig) {
                await this.initializeLevel(levelConfig);
            } else {
                // 加载默认关卡
                this.currentLevel = this.createDefaultLevel();
                if (this.currentLevel && typeof this.currentLevel.load === 'function') {
                    await this.currentLevel.load();
                }
            }

            // 设置游戏状态
            this.gameState = 'playing';
            this.isRunning = true;

            // 开始游戏循环
            this.lastTime = performance.now();
            this.gameLoop();

            // 隐藏菜单，显示游戏界面
            this.showGameScreen();

            console.log('🎮 游戏开始');

        } catch (error) {
            console.error('❌ 游戏启动失败:', error);
            this.showError('游戏启动失败: ' + error.message);
        }
    }

    /**
     * 开始指定关卡
     * @param {string} levelId - 关卡ID
     * @param {string} difficulty - 难度等级
     */
    async startLevel(levelId, difficulty = 'normal') {
        if (!this.isInitialized) {
            console.error('❌ 游戏未初始化');
            return;
        }

        try {
            console.log(`🎮 开始关卡: ${levelId} (${difficulty})`);

            // 确保音频引擎已初始化
            if (window.audioEngine && !audioEngine.isReady()) {
                await audioEngine.init();
            }

            // 重置游戏状态
            this.resetGameState();

            // 通过关卡管理器加载关卡
            if (window.levelManager) {
                const success = await levelManager.loadLevel(levelId, difficulty);
                if (!success) {
                    throw new Error(`无法加载关卡 ${levelId} (${difficulty})`);
                }
                this.currentLevel = levelManager.currentLevel;
            } else {
                // 备用方案：创建基于ID的关卡配置
                const levelConfig = this.createLevelConfigById(levelId, difficulty);
                if (window.Level) {
                    this.currentLevel = new Level(levelConfig);
                    await this.currentLevel.load();
                } else {
                    // 如果Level类不存在，直接使用配置
                    this.currentLevel = levelConfig;
                }
            }

            // 设置游戏状态
            this.gameState = 'playing';
            this.isRunning = true;

            // 开始游戏循环
            this.lastTime = performance.now();
            this.gameLoop();

            // 显示游戏界面
            this.showGameScreen();

            console.log(`✅ 关卡 ${levelId} 启动成功`);

            // 显示关卡开始通知
            this.showGameNotification(`开始关卡: ${levelId}`, `难度: ${difficulty}`);

            // 检查是否需要显示教学提示
            this.checkAndStartTutorial(levelId);

        } catch (error) {
            console.error('❌ 关卡启动失败:', error);
            this.showError(`关卡启动失败: ${error.message}`);
        }
    }

    /**
     * 检查并启动教学提示
     * @param {string} levelId - 关卡ID
     */
    checkAndStartTutorial(levelId) {
        // 检查是否有教学提示系统
        if (!window.tutorialSystem || !window.tutorialSystem.isInitialized) {
            console.log('📚 教学提示系统未初始化，跳过教程');
            return;
        }

        // 获取关卡配置
        let levelConfig = null;
        if (window.levelManager && levelManager.currentLevel) {
            levelConfig = levelManager.currentLevel;
        }

        // 检查关卡是否配置了教程
        if (!levelConfig || !levelConfig.tutorial || !levelConfig.tutorial.enabled) {
            console.log(`📚 关卡 ${levelId} 未配置教程`);
            return;
        }

        // 检查是否应该显示教程（首次游玩或用户设置）
        const shouldShowTutorial = this.shouldShowTutorial(levelId, levelConfig.tutorial);
        if (!shouldShowTutorial) {
            console.log(`📚 关卡 ${levelId} 跳过教程显示`);
            return;
        }

        // 延迟启动教程，确保游戏界面已完全加载
        setTimeout(() => {
            const tutorialId = levelConfig.tutorial.tutorialId || 'level1';
            console.log(`📚 启动关卡 ${levelId} 的教程: ${tutorialId}`);

            // 设置教程回调
            window.tutorialSystem.on('onComplete', () => {
                console.log('📚 教程完成，继续游戏');
                // 可以在这里添加教程完成后的逻辑
            });

            window.tutorialSystem.on('onSkip', () => {
                console.log('📚 教程已跳过');
                // 可以在这里添加教程跳过后的逻辑
            });

            // 启动教程
            window.tutorialSystem.startTutorial(tutorialId);
        }, 1000); // 延迟1秒确保界面加载完成
    }

    /**
     * 判断是否应该显示教程
     * @param {string} levelId - 关卡ID
     * @param {Object} tutorialConfig - 教程配置
     * @returns {boolean}
     */
    shouldShowTutorial(levelId, tutorialConfig) {
        // 检查用户设置是否启用教程
        const userSettings = window.storage ? storage.get('gameSettings', {}) : {};
        if (userSettings.showTutorial === false) {
            return false;
        }

        // 检查是否是首次游玩
        if (tutorialConfig.showOnFirstPlay) {
            const playHistory = window.storage ? storage.get('levelPlayHistory', {}) : {};
            if (!playHistory[levelId]) {
                // 记录首次游玩
                playHistory[levelId] = {
                    firstPlayTime: Date.now(),
                    playCount: 1
                };
                if (window.storage) {
                    storage.set('levelPlayHistory', playHistory);
                }
                return true;
            }
        }

        // 检查是否设置为自动启动
        return tutorialConfig.autoStart === true;
    }

    /**
     * 游戏循环
     */
    gameLoop() {
        if (!this.isRunning) return;
        
        const currentTime = performance.now();
        let deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        
        // 限制最大帧时间，避免螺旋死亡
        deltaTime = Math.min(deltaTime, this.maxFrameTime);
        
        this.accumulator += deltaTime;
        
        // 固定时间步长更新
        while (this.accumulator >= this.fixedTimeStep) {
            this.update(this.fixedTimeStep);
            this.accumulator -= this.fixedTimeStep;
        }
        
        // 渲染（使用插值）
        const alpha = this.accumulator / this.fixedTimeStep;
        this.render(alpha);
        
        // 继续循环
        requestAnimationFrame(() => this.gameLoop());
    }

    /**
     * 更新游戏逻辑
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (this.gameState !== 'playing') return;

        // 更新输入管理器（检查是否存在）
        if (this.inputManager) {
            try {
                this.inputManager.update(deltaTime);
            } catch (error) {
                console.error('❌ 输入管理器更新失败:', error);
            }
        } else {
            // 只在第一次显示警告，避免日志刷屏
            if (!this._inputManagerWarningShown) {
                console.warn('⚠️ 输入管理器不可用，跳过更新');
                this._inputManagerWarningShown = true;
            }
        }

        // 更新物理引擎（检查是否存在）
        if (window.physicsEngine && typeof physicsEngine.update === 'function') {
            try {
                physicsEngine.update(deltaTime);
            } catch (error) {
                console.error('❌ 物理引擎更新失败:', error);
            }
        }

        // 更新量子引擎（检查是否存在）
        if (window.quantumEngine && typeof quantumEngine.update === 'function') {
            try {
                quantumEngine.update(deltaTime);
            } catch (error) {
                console.error('❌ 量子引擎更新失败:', error);
            }
        }

        // 更新当前关卡
        if (this.currentLevel) {
            try {
                this.currentLevel.update(deltaTime);
            } catch (error) {
                console.error('❌ 关卡更新失败:', error);
            }
        }

        // 检查游戏结束条件
        try {
            this.checkGameEndConditions();
        } catch (error) {
            console.error('❌ 游戏结束条件检查失败:', error);
        }
    }

    /**
     * 渲染游戏
     * @param {number} alpha - 插值参数
     */
    render(alpha) {
        // 渲染游戏（检查渲染引擎是否存在）
        if (window.renderEngine && typeof renderEngine.render === 'function') {
            renderEngine.render(alpha);
        }
    }

    /**
     * 处理指针输入
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    handlePointerInput(x, y) {
        if (this.gameState !== 'playing') return;
        
        // 转换为世界坐标
        const worldPos = renderEngine.screenToWorld(x, y);
        
        // 查找点击的粒子
        const clickedParticle = this.findParticleAt(worldPos.x, worldPos.y);
        
        if (clickedParticle) {
            // 尝试激活粒子
            const success = quantumEngine.activateParticle(
                clickedParticle, 
                quantumEngine.targetFrequency
            );
            
            if (success) {
                console.log(`🎯 激活粒子: ${clickedParticle.id}`);
            }
        }
    }

    /**
     * 处理指针移动
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    handlePointerMove(x, y) {
        // 可以用于实时频率调节或其他交互
    }

    /**
     * 处理键盘输入
     * @param {string} key - 按键
     */
    handleKeyInput(key) {
        switch (key) {
            case 'Escape':
                if (this.gameState === 'playing') {
                    this.pauseGame();
                } else if (this.gameState === 'paused') {
                    this.resumeGame();
                }
                break;
                
            case 'Space':
                if (this.gameState === 'playing') {
                    // 空格键可以用于特殊操作
                    this.handleSpaceKey();
                }
                break;
                
            case 'KeyR':
                if (this.gameState === 'gameOver') {
                    this.restartGame();
                }
                break;
        }
    }

    /**
     * 处理频率输入
     * @param {number} frequency - 频率值
     */
    handleFrequencyInput(frequency) {
        quantumEngine.setTargetFrequency(frequency);
    }

    /**
     * 查找指定位置的粒子
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @returns {Object|null} 粒子对象
     */
    findParticleAt(x, y) {
        for (const particle of physicsEngine.particles) {
            const distance = MathUtils.distance(x, y, particle.x, particle.y);
            if (distance <= particle.radius) {
                return particle;
            }
        }
        return null;
    }

    /**
     * 粒子激活回调
     * @param {Object} particle - 粒子对象
     * @param {number} strength - 激活强度
     */
    onParticleActivated(particle, strength) {
        // 可以添加视觉特效、音效等
        console.log(`⚛️ 粒子激活回调: ${particle.id}, 强度: ${strength.toFixed(2)}`);
    }

    /**
     * 连锁反应回调
     * @param {Object} chain - 连锁反应对象
     */
    onChainReaction(chain) {
        // 可以添加特殊效果
        console.log(`🔗 连锁反应回调: 长度 ${chain.length}`);
    }

    /**
     * 分数更新回调
     * @param {number} totalScore - 总分
     * @param {number} scoreIncrease - 分数增量
     */
    onScoreUpdate(totalScore, scoreIncrease) {
        // 更新UI显示
        this.updateScoreDisplay(totalScore, scoreIncrease);
    }

    /**
     * 连击更新回调
     * @param {number} combo - 连击数
     */
    onComboUpdate(combo) {
        // 更新UI显示
        this.updateComboDisplay(combo);
    }

    /**
     * 暂停游戏
     */
    pauseGame() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            this.isRunning = false;

            // 暂停音频
            audioEngine.suspend();

            // 显示暂停界面
            this.showPauseScreen();

            // 显示暂停通知
            this.showInfoNotification('游戏已暂停');

            console.log('⏸️ 游戏已暂停');
        }
    }

    /**
     * 恢复游戏
     */
    resumeGame() {
        if (this.gameState === 'paused') {
            this.gameState = 'playing';
            this.isRunning = true;

            // 恢复音频
            audioEngine.resume();

            // 隐藏暂停界面
            this.hidePauseScreen();

            // 重新开始游戏循环
            this.lastTime = performance.now();
            this.gameLoop();

            // 显示恢复通知
            this.showSuccessNotification('游戏已恢复');

            console.log('▶️ 游戏已恢复');
        }
    }

    /**
     * 重启游戏
     */
    restartGame() {
        this.resetGameState();
        this.startGame(this.currentLevel ? this.currentLevel.config : null);
    }

    /**
     * 结束游戏
     */
    endGame() {
        this.gameState = 'gameOver';
        this.isRunning = false;
        
        // 停止音频
        audioEngine.suspend();
        
        // 保存最高分
        this.saveHighScore();

        // 更新玩家统计数据
        this.updatePlayerStats();

        // 显示游戏结束界面
        this.showGameOverScreen();
        
        console.log('🏁 游戏结束');
    }

    /**
     * 重置游戏状态
     */
    resetGameState() {
        // 重置引擎（检查是否存在）
        if (window.physicsEngine) {
            physicsEngine.clear();
        }
        if (window.quantumEngine) {
            quantumEngine.reset();
        }

        // 重置状态
        this.gameState = 'menu';
        this.isRunning = false;
        this.accumulator = 0;
    }

    /**
     * 检查游戏结束条件
     */
    checkGameEndConditions() {
        if (!this.currentLevel) return;
        
        // 检查关卡完成条件
        if (this.currentLevel.isCompleted()) {
            this.completeLevel();
        }
        
        // 检查失败条件
        if (this.currentLevel.isFailed()) {
            this.endGame();
        }
    }

    /**
     * 完成关卡
     */
    completeLevel() {
        console.log('🎉 关卡完成!');

        // 计算奖励分数
        const bonusScore = this.currentLevel.calculateBonusScore();
        quantumEngine.score += bonusScore;

        // 显示关卡完成通知
        this.showSuccessNotification(`关卡完成！获得奖励分数: ${bonusScore}`);

        // 进入下一关卡或结束游戏
        this.nextLevel();
    }

    /**
     * 下一关卡
     */
    nextLevel() {
        // 这里可以加载下一个关卡
        // 暂时结束游戏
        this.endGame();
    }

    /**
     * 创建默认关卡
     * @returns {Level} 关卡对象
     */
    createDefaultLevel() {
        const config = {
            name: '量子共鸣 - 第一关',
            description: '学习基本的粒子激活和共鸣机制',
            particles: [
                { x: 200, y: 200, frequency: 440, radius: 10 },
                { x: 400, y: 200, frequency: 880, radius: 10 },
                { x: 300, y: 300, frequency: 660, radius: 10 },
                { x: 500, y: 300, frequency: 330, radius: 10 },
                { x: 350, y: 150, frequency: 550, radius: 10 }
            ],
            targetScore: 1000,
            timeLimit: 60
        };

        return new Level(config);
    }

    /**
     * 根据关卡ID创建关卡配置（备用方案）
     * @param {string} levelId - 关卡ID
     * @param {string} difficulty - 难度等级
     * @returns {Object} 关卡配置对象
     */
    createLevelConfigById(levelId, difficulty) {
        // 基础配置模板
        const baseConfig = {
            id: levelId,
            difficulty: difficulty,
            particles: [],
            targetScore: 1000,
            timeLimit: 60
        };

        // 根据关卡ID生成不同的配置
        switch (levelId) {
            case 'quantum_basics':
                return {
                    ...baseConfig,
                    name: '量子基础',
                    description: '学习基本的量子粒子操控',
                    particles: [
                        { x: 200, y: 200, frequency: 440, radius: 8 },
                        { x: 400, y: 200, frequency: 880, radius: 8 },
                        { x: 300, y: 300, frequency: 660, radius: 8 }
                    ],
                    targetScore: difficulty === 'easy' ? 500 : difficulty === 'hard' ? 1500 : 1000
                };

            case 'resonance_chains':
                return {
                    ...baseConfig,
                    name: '共鸣链条',
                    description: '创造连锁共鸣反应',
                    particles: [
                        { x: 150, y: 150, frequency: 330, radius: 10 },
                        { x: 300, y: 150, frequency: 440, radius: 10 },
                        { x: 450, y: 150, frequency: 550, radius: 10 },
                        { x: 225, y: 300, frequency: 660, radius: 10 },
                        { x: 375, y: 300, frequency: 770, radius: 10 }
                    ],
                    targetScore: difficulty === 'easy' ? 800 : difficulty === 'hard' ? 2000 : 1200
                };

            case 'harmonic_patterns':
                return {
                    ...baseConfig,
                    name: '和谐模式',
                    description: '掌握复杂的和谐频率模式',
                    particles: [
                        { x: 200, y: 100, frequency: 220, radius: 12 },
                        { x: 400, y: 100, frequency: 440, radius: 12 },
                        { x: 600, y: 100, frequency: 880, radius: 12 },
                        { x: 300, y: 250, frequency: 330, radius: 12 },
                        { x: 500, y: 250, frequency: 660, radius: 12 },
                        { x: 400, y: 400, frequency: 550, radius: 12 }
                    ],
                    targetScore: difficulty === 'easy' ? 1200 : difficulty === 'hard' ? 3000 : 1800
                };

            default:
                // 默认配置
                return {
                    ...baseConfig,
                    name: `关卡 ${levelId}`,
                    description: '探索量子共鸣的奥秘',
                    particles: [
                        { x: 200, y: 200, frequency: 440, radius: 10 },
                        { x: 400, y: 200, frequency: 880, radius: 10 },
                        { x: 300, y: 300, frequency: 660, radius: 10 }
                    ]
                };
        }
    }

    /**
     * 加载设置
     */
    async loadSettings() {
        try {
            // 检查存储服务是否可用
            if (window.storageService && typeof storageService.get === 'function') {
                const savedSettings = await storageService.get('game.settings');
                if (savedSettings) {
                    Object.assign(this.settings, savedSettings);
                    console.log('✅ 游戏设置加载完成');
                } else {
                    console.log('📋 使用默认游戏设置');
                }
            } else {
                console.warn('⚠️ 存储服务不可用，使用默认设置');
            }
        } catch (error) {
            console.warn('⚠️ 设置加载失败，使用默认设置:', error.message);
        }
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            await storageService.put('game.settings', this.settings);
        } catch (error) {
            console.error('❌ 设置保存失败:', error);
        }
    }

    /**
     * 应用设置
     */
    applySettings() {
        try {
            // 应用音频设置
            if (window.audioEngine && typeof audioEngine.isReady === 'function' && audioEngine.isReady()) {
                audioEngine.setVolume('master', this.settings.masterVolume);
                audioEngine.setVolume('music', this.settings.musicVolume);
                audioEngine.setVolume('sfx', this.settings.sfxVolume);
                console.log('✅ 音频设置已应用');
            } else {
                console.warn('⚠️ 音频引擎不可用，跳过音频设置');
            }

            // 应用渲染设置
            if (window.renderEngine) {
                renderEngine.enableParticleTrails = this.settings.enableParticleEffects;
                renderEngine.enableGlowEffects = this.settings.enableParticleEffects;
                console.log('✅ 渲染设置已应用');
            } else {
                console.warn('⚠️ 渲染引擎不可用，跳过渲染设置');
            }
        } catch (error) {
            console.warn('⚠️ 设置应用失败:', error.message);
        }
    }

    /**
     * 保存最高分
     */
    async saveHighScore() {
        try {
            const currentScore = quantumEngine.score;
            const highScore = await storageService.get('game.highScore') || 0;
            
            if (currentScore > highScore) {
                await storageService.put('game.highScore', currentScore);
                console.log('🏆 新的最高分!', currentScore);
            }
        } catch (error) {
            console.error('❌ 最高分保存失败:', error);
        }
    }

    // UI 相关方法（这些方法需要与HTML界面交互）
    showMainMenu() {
        if (window.uiManager) {
            uiManager.showScreen('main-menu-screen');
        }
    }

    showGameScreen() {
        console.log('🎮 游戏控制器：开始显示游戏屏幕');
        if (window.uiManager) {
            console.log('🎮 游戏控制器：调用 uiManager.showScreen("game-screen")');
            uiManager.showScreen('game-screen');

            // 添加保险措施：确保游戏屏幕一定会显示
            setTimeout(() => {
                const gameScreen = document.getElementById('game-screen');
                if (gameScreen) {
                    const computedStyle = window.getComputedStyle(gameScreen);
                    const isVisible = computedStyle.opacity === '1' && computedStyle.visibility === 'visible';

                    console.log(`🎮 游戏屏幕状态检查: opacity=${computedStyle.opacity}, visibility=${computedStyle.visibility}, display=${computedStyle.display}`);

                    if (!isVisible) {
                        console.warn('⚠️ 游戏屏幕未正确显示，执行强制显示');
                        gameScreen.classList.add('active');
                        gameScreen.style.opacity = '1';
                        gameScreen.style.visibility = 'visible';
                        gameScreen.style.display = 'flex';
                        gameScreen.style.zIndex = '10';
                        console.log('🔧 强制显示游戏屏幕完成');
                    } else {
                        console.log('✅ 游戏屏幕显示正常');
                    }
                } else {
                    console.error('❌ 找不到游戏屏幕元素 #game-screen');
                }
            }, 200); // 等待UI管理器完成屏幕切换
        } else {
            console.error('❌ UI管理器未初始化');
        }
    }

    showPauseScreen() {
        if (window.uiManager) {
            uiManager.showScreen('pause-screen');
        }
    }

    hidePauseScreen() {
        if (window.uiManager) {
            // 暂停屏幕会自动隐藏，这里可以切换到游戏屏幕
            uiManager.showScreen('game-screen');
        }
    }

    showGameOverScreen() {
        // 计算游戏结果
        const gameResult = this.calculateGameResult();

        // 显示游戏结束屏幕
        if (window.gameOver) {
            gameOver.show(gameResult);
        } else {
            // 备用显示方式
            document.getElementById('gameOverScreen').style.display = 'block';
        }
    }

    showError(message) {
        console.error('❌', message);

        // 使用通知系统替代alert弹框
        if (window.notificationSystem) {
            window.notificationSystem.error(message, {
                title: '游戏错误',
                duration: 6000
            });
        } else {
            // 备用方案：如果通知系统未加载，仍使用alert
            alert(message);
        }
    }

    /**
     * 显示游戏通知
     * @param {string} message - 消息内容
     * @param {string} subtitle - 副标题
     */
    showGameNotification(message, subtitle = '') {
        if (window.notificationSystem) {
            const fullMessage = subtitle ? `${message}\n${subtitle}` : message;
            window.notificationSystem.game(fullMessage, {
                title: '游戏',
                duration: 4000
            });
        }
    }

    /**
     * 显示成功通知
     * @param {string} message - 消息内容
     */
    showSuccessNotification(message) {
        if (window.notificationSystem) {
            window.notificationSystem.success(message, {
                title: '成功',
                duration: 3000
            });
        }
    }

    /**
     * 显示信息通知
     * @param {string} message - 消息内容
     */
    showInfoNotification(message) {
        if (window.notificationSystem) {
            window.notificationSystem.info(message, {
                title: '信息',
                duration: 4000
            });
        }
    }

    updateScoreDisplay(totalScore, scoreIncrease) {
        const scoreElement = document.getElementById('scoreValue');
        if (scoreElement) {
            scoreElement.textContent = totalScore;
        }
    }

    updateComboDisplay(combo) {
        const comboElement = document.getElementById('comboValue');
        if (comboElement) {
            comboElement.textContent = combo;
        }
    }

    handleSpaceKey() {
        // 空格键的特殊功能，比如释放特殊技能
        console.log('🚀 空格键按下');
    }

    /**
     * 计算游戏结果
     * @returns {Object} 游戏结果数据
     */
    calculateGameResult() {
        // 获取量子引擎的数据（使用新增的统计属性）
        const quantumData = window.quantumEngine ? {
            score: quantumEngine.score || 0,
            combo: quantumEngine.combo || 0,
            maxCombo: quantumEngine.maxCombo || 0,
            level: quantumEngine.level || 1,
            fieldStrength: quantumEngine.fieldStrength || 1.0,
            activeWaves: quantumEngine.energyWaves ? quantumEngine.energyWaves.length : 0,
            activeChains: quantumEngine.resonanceChains ? quantumEngine.resonanceChains.length : 0,
            particlesActivated: quantumEngine.particlesActivated || 0,
            chainReactions: quantumEngine.chainReactions || 0,
            perfectHits: quantumEngine.perfectHits || 0,
            totalHits: quantumEngine.totalHits || 0
        } : {
            score: 0,
            combo: 0,
            maxCombo: 0,
            level: 1,
            fieldStrength: 1.0,
            activeWaves: 0,
            activeChains: 0,
            particlesActivated: 0,
            chainReactions: 0,
            perfectHits: 0,
            totalHits: 0
        };

        // 计算准确率
        const accuracy = quantumData.totalHits > 0 ? (quantumData.perfectHits / quantumData.totalHits) : 0;

        // 获取关卡数据
        const levelData = this.currentLevel ? {
            name: this.currentLevel.name || '未知关卡',
            id: this.currentLevel.id || 'unknown',
            difficulty: this.currentLevel.difficulty || 'normal',
            completed: this.currentLevel.isCompleted ? this.currentLevel.isCompleted() : false,
            failed: this.currentLevel.isFailed ? this.currentLevel.isFailed() : false,
            timeElapsed: this.currentLevel.elapsedTime || 0,
            timeLimit: this.currentLevel.timeLimit || 60,
            targetScore: this.currentLevel.targetScore || 1000
        } : {
            name: '未知关卡',
            id: 'unknown',
            difficulty: 'normal',
            completed: false,
            failed: true,
            timeElapsed: 0,
            timeLimit: 60,
            targetScore: 1000
        };

        // 计算星级评价
        let stars = 0;
        if (levelData.completed) {
            stars = 1; // 完成关卡获得1星

            // 达到目标分数获得2星
            if (quantumData.score >= levelData.targetScore) {
                stars = 2;
            }

            // 在时间限制内高效完成获得3星
            if (levelData.timeElapsed <= levelData.timeLimit * 0.7 && accuracy >= 0.8) {
                stars = 3;
            }
        }

        // 返回完整的游戏结果
        return {
            // 基本信息
            completed: levelData.completed,
            failed: levelData.failed,
            levelName: levelData.name,
            levelId: levelData.id,
            difficulty: levelData.difficulty,
            stars: stars,

            // 分数和统计
            score: quantumData.score,
            time: levelData.timeElapsed,
            particlesActivated: quantumData.particlesActivated,
            chainReactions: quantumData.chainReactions,
            maxCombo: quantumData.maxCombo,
            perfectHits: quantumData.perfectHits,
            accuracy: accuracy,
            efficiency: quantumData.fieldStrength / 2.0, // 基于场强度计算效率

            // 量子引擎特有数据
            level: quantumData.level,
            fieldStrength: quantumData.fieldStrength,
            activeWaves: quantumData.activeWaves,
            activeChains: quantumData.activeChains,

            // 关卡目标
            targetScore: levelData.targetScore,
            timeLimit: levelData.timeLimit,

            // 时间戳
            timestamp: Date.now()
        };
    }

    /**
     * 更新玩家统计数据
     */
    updatePlayerStats() {
        if (!window.playerManager) return;

        // 计算游戏结果
        const gameResult = this.calculateGameResult();

        // 更新玩家统计
        const stats = {
            gamesPlayed: 1,
            totalScore: gameResult.score,
            totalTime: gameResult.time,
            particlesActivated: gameResult.particlesActivated || 0,
            chainReactions: gameResult.chainReactions || 0,
            maxCombo: gameResult.maxCombo || 0,
            perfectHits: gameResult.perfectHits || 0
        };

        playerManager.updatePlayerStats(stats);

        // 更新关卡进度
        if (this.currentLevel && gameResult.completed) {
            const levelProgress = {
                completed: true,
                bestScore: gameResult.score,
                bestTime: gameResult.time,
                stars: gameResult.stars || 0
            };

            playerManager.updateLevelProgress(this.currentLevel.id, levelProgress);
        }

        // 更新排行榜
        const leaderboardEntry = {
            score: gameResult.score,
            time: gameResult.time,
            combo: gameResult.maxCombo || 0,
            chainReactions: gameResult.chainReactions || 0,
            levelName: this.currentLevel ? this.currentLevel.name : '未知关卡',
            timestamp: Date.now()
        };

        // 根据游戏结果更新不同类别的排行榜
        if (gameResult.score > 0) {
            playerManager.updateLeaderboard('highScores', leaderboardEntry);
        }
        if (gameResult.time > 0) {
            playerManager.updateLeaderboard('fastestTimes', leaderboardEntry);
        }
        if (gameResult.maxCombo > 0) {
            playerManager.updateLeaderboard('longestCombos', leaderboardEntry);
        }
        if (gameResult.chainReactions > 0) {
            playerManager.updateLeaderboard('mostChainReactions', leaderboardEntry);
        }

        // 检查成就
        const unlockedAchievements = playerManager.checkAchievements();

        // 显示成就通知
        if (unlockedAchievements.length > 0 && window.uiManager) {
            unlockedAchievements.forEach(achievement => {
                uiManager.showAchievementToast(achievement);
            });
        }

        console.log('📊 玩家统计数据已更新');
    }

    /**
     * 销毁游戏控制器
     */
    destroy() {
        this.isRunning = false;
        
        if (this.inputManager) {
            this.inputManager.destroy();
        }
        
        // 销毁引擎（检查是否存在）
        if (window.audioEngine && typeof audioEngine.destroy === 'function') {
            audioEngine.destroy();
        }
        if (window.renderEngine && typeof renderEngine.destroy === 'function') {
            renderEngine.destroy();
        }
        if (window.quantumEngine && typeof quantumEngine.destroy === 'function') {
            quantumEngine.destroy();
        }
        
        console.log('🎮 游戏控制器已销毁');
    }
}

// 导出类到全局作用域
window.GameController = GameController;

// 创建全局游戏控制器实例
window.gameController = new GameController();

// 页面加载完成后自动初始化游戏控制器
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('🎮 页面加载完成，开始初始化游戏控制器...');
        await window.gameController.init();
        console.log('🎮 游戏控制器自动初始化完成');
    } catch (error) {
        console.error('❌ 游戏控制器自动初始化失败:', error);
    }
});
