/**
 * 量子共鸣者 - 输入管理器
 * 统一处理鼠标、触摸、键盘、音频输入等各种输入方式
 */

/**
 * 手势检测器
 */
class GestureDetector {
    constructor() {
        this.pointers = new Map();
        this.gestures = [];
        this.currentGesture = null;

        // 手势阈值
        this.tapThreshold = 10; // 点击阈值（像素）
        this.tapTimeThreshold = 300; // 点击时间阈值（毫秒）
        this.swipeThreshold = 50; // 滑动阈值（像素）
        this.pinchThreshold = 20; // 缩放阈值（像素）

        // 事件回调
        this.onTap = null;
        this.onDoubleTap = null;
        this.onSwipe = null;
        this.onPinch = null;
        this.onRotate = null;

        console.log('👆 手势检测器已创建');
    }

    /**
     * 初始化手势检测器
     * @param {HTMLCanvasElement} canvas - 画布元素
     */
    init(canvas) {
        this.canvas = canvas;
        console.log('👆 手势检测器初始化完成');
    }

    /**
     * 处理指针按下事件
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} pointerId - 指针ID
     */
    onPointerDown(x, y, pointerId) {
        const pointer = {
            id: pointerId,
            startX: x,
            startY: y,
            currentX: x,
            currentY: y,
            startTime: Date.now(),
            isActive: true
        };

        this.pointers.set(pointerId, pointer);

        // 检测多点触控手势
        if (this.pointers.size === 2) {
            this.startMultiTouchGesture();
        }
    }

    /**
     * 处理指针移动事件
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} pointerId - 指针ID
     */
    onPointerMove(x, y, pointerId) {
        const pointer = this.pointers.get(pointerId);
        if (!pointer) return;

        pointer.currentX = x;
        pointer.currentY = y;

        // 更新多点触控手势
        if (this.pointers.size === 2) {
            this.updateMultiTouchGesture();
        }
    }

    /**
     * 处理指针抬起事件
     * @param {number} pointerId - 指针ID
     */
    onPointerUp(pointerId) {
        const pointer = this.pointers.get(pointerId);
        if (!pointer) return;

        const endTime = Date.now();
        const duration = endTime - pointer.startTime;
        const deltaX = pointer.currentX - pointer.startX;
        const deltaY = pointer.currentY - pointer.startY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // 检测点击手势
        if (distance < this.tapThreshold && duration < this.tapTimeThreshold) {
            this.handleTap(pointer.startX, pointer.startY);
        }
        // 检测滑动手势
        else if (distance > this.swipeThreshold) {
            this.handleSwipe(deltaX, deltaY, duration);
        }

        this.pointers.delete(pointerId);

        // 结束多点触控手势
        if (this.pointers.size < 2) {
            this.endMultiTouchGesture();
        }
    }

    /**
     * 处理点击手势
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    handleTap(x, y) {
        if (this.onTap) {
            this.onTap(x, y);
        }
    }

    /**
     * 处理滑动手势
     * @param {number} deltaX - X方向位移
     * @param {number} deltaY - Y方向位移
     * @param {number} duration - 持续时间
     */
    handleSwipe(deltaX, deltaY, duration) {
        const direction = this.getSwipeDirection(deltaX, deltaY);
        const velocity = Math.sqrt(deltaX * deltaX + deltaY * deltaY) / duration;

        if (this.onSwipe) {
            this.onSwipe(direction, velocity);
        }
    }

    /**
     * 获取滑动方向
     * @param {number} deltaX - X方向位移
     * @param {number} deltaY - Y方向位移
     * @returns {string} 方向
     */
    getSwipeDirection(deltaX, deltaY) {
        const absX = Math.abs(deltaX);
        const absY = Math.abs(deltaY);

        if (absX > absY) {
            return deltaX > 0 ? 'right' : 'left';
        } else {
            return deltaY > 0 ? 'down' : 'up';
        }
    }

    /**
     * 开始多点触控手势
     */
    startMultiTouchGesture() {
        const pointers = Array.from(this.pointers.values());
        if (pointers.length !== 2) return;

        const [p1, p2] = pointers;
        this.currentGesture = {
            type: 'multitouch',
            initialDistance: this.getDistance(p1, p2),
            initialAngle: this.getAngle(p1, p2),
            centerX: (p1.startX + p2.startX) / 2,
            centerY: (p1.startY + p2.startY) / 2
        };
    }

    /**
     * 更新多点触控手势
     */
    updateMultiTouchGesture() {
        if (!this.currentGesture || this.currentGesture.type !== 'multitouch') return;

        const pointers = Array.from(this.pointers.values());
        if (pointers.length !== 2) return;

        const [p1, p2] = pointers;
        const currentDistance = this.getDistance(p1, p2);
        const currentAngle = this.getAngle(p1, p2);

        // 检测缩放手势
        const scaleChange = currentDistance / this.currentGesture.initialDistance;
        if (Math.abs(scaleChange - 1) > 0.1) {
            if (this.onPinch) {
                this.onPinch(scaleChange, this.currentGesture.centerX, this.currentGesture.centerY);
            }
        }

        // 检测旋转手势
        const angleChange = currentAngle - this.currentGesture.initialAngle;
        if (Math.abs(angleChange) > 0.1) {
            if (this.onRotate) {
                this.onRotate(angleChange, this.currentGesture.centerX, this.currentGesture.centerY);
            }
        }
    }

    /**
     * 结束多点触控手势
     */
    endMultiTouchGesture() {
        this.currentGesture = null;
    }

    /**
     * 计算两点间距离
     * @param {Object} p1 - 第一个点
     * @param {Object} p2 - 第二个点
     * @returns {number} 距离
     */
    getDistance(p1, p2) {
        const dx = p2.currentX - p1.currentX;
        const dy = p2.currentY - p1.currentY;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 计算两点间角度
     * @param {Object} p1 - 第一个点
     * @param {Object} p2 - 第二个点
     * @returns {number} 角度（弧度）
     */
    getAngle(p1, p2) {
        return Math.atan2(p2.currentY - p1.currentY, p2.currentX - p1.currentX);
    }

    /**
     * 更新手势检测器
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 清理过期的手势
        this.gestures = this.gestures.filter(gesture => {
            return Date.now() - gesture.timestamp < 1000; // 1秒过期
        });
    }

    /**
     * 销毁手势检测器
     */
    destroy() {
        this.pointers.clear();
        this.gestures = [];
        this.currentGesture = null;
        console.log('👆 手势检测器已销毁');
    }
}

class InputManager {
    constructor() {
        this.canvas = null;
        this.isEnabled = true;
        
        // 指针状态
        this.pointers = new Map(); // 支持多点触控
        this.lastPointerPosition = { x: 0, y: 0 };
        
        // 键盘状态
        this.keys = new Set();
        this.keyBindings = new Map();
        
        // 音频输入
        this.audioInput = null;
        this.microphoneStream = null;
        this.frequencyAnalyzer = null;
        this.isListeningToMicrophone = false;
        
        // 频率控制
        this.currentFrequency = 440;
        this.frequencyRange = { min: 20, max: 20000 };
        
        // 事件回调
        this.onPointerDown = null;
        this.onPointerMove = null;
        this.onPointerUp = null;
        this.onKeyDown = null;
        this.onKeyUp = null;
        this.onFrequencyChange = null;
        
        // 手势识别
        this.gestureDetector = new GestureDetector();
        
        console.log('🎮 输入管理器已创建');
    }

    /**
     * 初始化输入管理器
     * @param {HTMLCanvasElement} canvas - 游戏画布
     */
    init(canvas) {
        this.canvas = canvas;
        
        // 设置事件监听器
        this.setupPointerEvents();
        this.setupKeyboardEvents();
        this.setupAudioInput();
        this.setupFrequencyControls();
        
        // 初始化手势检测
        this.gestureDetector.init(canvas);
        
        console.log('🎮 输入管理器初始化完成');
    }

    /**
     * 设置指针事件（鼠标和触摸）
     */
    setupPointerEvents() {
        if (!this.canvas) return;
        
        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => this.handlePointerDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handlePointerMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handlePointerUp(e));
        this.canvas.addEventListener('wheel', (e) => this.handleWheel(e));
        
        // 触摸事件
        this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
        this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));
        
        // 防止默认的触摸行为
        this.canvas.addEventListener('touchstart', (e) => e.preventDefault());
        this.canvas.addEventListener('touchmove', (e) => e.preventDefault());
    }

    /**
     * 设置键盘事件
     */
    setupKeyboardEvents() {
        // 键盘事件需要在document上监听
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // 设置默认按键绑定
        this.setupDefaultKeyBindings();
    }

    /**
     * 设置默认按键绑定
     */
    setupDefaultKeyBindings() {
        this.keyBindings.set('Escape', 'pause');
        this.keyBindings.set('Space', 'action');
        this.keyBindings.set('KeyR', 'restart');
        this.keyBindings.set('KeyM', 'mute');
        this.keyBindings.set('ArrowUp', 'frequencyUp');
        this.keyBindings.set('ArrowDown', 'frequencyDown');
        this.keyBindings.set('ArrowLeft', 'frequencyDown');
        this.keyBindings.set('ArrowRight', 'frequencyUp');
    }

    /**
     * 设置音频输入
     */
    async setupAudioInput() {
        try {
            // 检查浏览器支持
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                console.warn('⚠️ 浏览器不支持音频输入');
                return;
            }
            
            // 创建音频输入按钮事件
            const micButton = document.getElementById('microphoneButton');
            if (micButton) {
                micButton.addEventListener('click', () => this.toggleMicrophone());
            }
            
        } catch (error) {
            console.error('❌ 音频输入设置失败:', error);
        }
    }

    /**
     * 设置频率控制
     */
    setupFrequencyControls() {
        console.log('🎚️ 设置频率控制...');

        // 频率滑块 - 使用正确的ID
        const frequencySlider = document.getElementById('frequency-slider');
        if (frequencySlider) {
            console.log('✅ 找到频率滑块，绑定事件');
            frequencySlider.addEventListener('input', (e) => {
                const frequency = parseFloat(e.target.value);
                console.log(`🎚️ 滑块频率变化: ${frequency} Hz`);
                this.setFrequency(frequency);
            });
        } else {
            console.warn('⚠️ 未找到频率滑块元素 (frequency-slider)');
        }

        // 频率输入框
        const frequencyInput = document.getElementById('frequencyInput');
        if (frequencyInput) {
            console.log('✅ 找到频率输入框，绑定事件');
            frequencyInput.addEventListener('change', (e) => {
                const frequency = parseFloat(e.target.value);
                console.log(`📝 输入框频率变化: ${frequency} Hz`);
                this.setFrequency(frequency);
            });
        } else {
            console.log('ℹ️ 未找到频率输入框元素 (frequencyInput)');
        }

        // 预设频率按钮
        const presetButtons = document.querySelectorAll('.frequency-preset');
        if (presetButtons.length > 0) {
            console.log(`✅ 找到 ${presetButtons.length} 个预设频率按钮`);
            presetButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const frequency = parseFloat(e.target.dataset.frequency);
                    console.log(`🎯 预设频率按钮点击: ${frequency} Hz`);
                    this.setFrequency(frequency);
                });
            });
        } else {
            console.log('ℹ️ 未找到预设频率按钮');
        }
    }

    /**
     * 处理指针按下事件
     * @param {Event} e - 事件对象
     */
    handlePointerDown(e) {
        if (!this.isEnabled) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const pointerId = e.pointerId || 'mouse';
        this.pointers.set(pointerId, { x, y, startTime: Date.now() });
        
        this.lastPointerPosition = { x, y };
        
        if (this.onPointerDown) {
            this.onPointerDown(x, y);
        }
        
        // 手势检测
        this.gestureDetector.onPointerDown(x, y, pointerId);
    }

    /**
     * 处理指针移动事件
     * @param {Event} e - 事件对象
     */
    handlePointerMove(e) {
        if (!this.isEnabled) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const pointerId = e.pointerId || 'mouse';
        if (this.pointers.has(pointerId)) {
            this.pointers.get(pointerId).x = x;
            this.pointers.get(pointerId).y = y;
        }
        
        this.lastPointerPosition = { x, y };
        
        if (this.onPointerMove) {
            this.onPointerMove(x, y);
        }
        
        // 手势检测
        this.gestureDetector.onPointerMove(x, y, pointerId);
    }

    /**
     * 处理指针抬起事件
     * @param {Event} e - 事件对象
     */
    handlePointerUp(e) {
        if (!this.isEnabled) return;
        
        const pointerId = e.pointerId || 'mouse';
        const pointer = this.pointers.get(pointerId);
        
        if (pointer) {
            const duration = Date.now() - pointer.startTime;
            this.pointers.delete(pointerId);
            
            if (this.onPointerUp) {
                this.onPointerUp(pointer.x, pointer.y, duration);
            }
        }
        
        // 手势检测
        this.gestureDetector.onPointerUp(pointerId);
    }

    /**
     * 处理触摸开始事件
     * @param {TouchEvent} e - 触摸事件
     */
    handleTouchStart(e) {
        for (const touch of e.changedTouches) {
            const mockEvent = {
                clientX: touch.clientX,
                clientY: touch.clientY,
                pointerId: touch.identifier
            };
            this.handlePointerDown(mockEvent);
        }
    }

    /**
     * 处理触摸移动事件
     * @param {TouchEvent} e - 触摸事件
     */
    handleTouchMove(e) {
        for (const touch of e.changedTouches) {
            const mockEvent = {
                clientX: touch.clientX,
                clientY: touch.clientY,
                pointerId: touch.identifier
            };
            this.handlePointerMove(mockEvent);
        }
    }

    /**
     * 处理触摸结束事件
     * @param {TouchEvent} e - 触摸事件
     */
    handleTouchEnd(e) {
        for (const touch of e.changedTouches) {
            const mockEvent = {
                pointerId: touch.identifier
            };
            this.handlePointerUp(mockEvent);
        }
    }

    /**
     * 处理滚轮事件
     * @param {WheelEvent} e - 滚轮事件
     */
    handleWheel(e) {
        if (!this.isEnabled) return;
        
        e.preventDefault();
        
        // 滚轮控制频率
        const delta = e.deltaY > 0 ? -10 : 10;
        this.adjustFrequency(delta);
    }

    /**
     * 处理按键按下事件
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyDown(e) {
        if (!this.isEnabled) return;
        
        const key = e.code;
        
        if (!this.keys.has(key)) {
            this.keys.add(key);
            
            // 处理按键绑定
            const action = this.keyBindings.get(key);
            if (action) {
                this.handleKeyAction(action, true);
            }
            
            if (this.onKeyDown) {
                this.onKeyDown(key);
            }
        }
    }

    /**
     * 处理按键抬起事件
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyUp(e) {
        if (!this.isEnabled) return;
        
        const key = e.code;
        this.keys.delete(key);
        
        const action = this.keyBindings.get(key);
        if (action) {
            this.handleKeyAction(action, false);
        }
        
        if (this.onKeyUp) {
            this.onKeyUp(key);
        }
    }

    /**
     * 处理按键动作
     * @param {string} action - 动作名称
     * @param {boolean} isPressed - 是否按下
     */
    handleKeyAction(action, isPressed) {
        if (!isPressed) return; // 只处理按下事件
        
        switch (action) {
            case 'frequencyUp':
                this.adjustFrequency(20);
                break;
            case 'frequencyDown':
                this.adjustFrequency(-20);
                break;
            case 'mute':
                this.toggleMute();
                break;
        }
    }

    /**
     * 切换麦克风
     */
    async toggleMicrophone() {
        if (this.isListeningToMicrophone) {
            this.stopMicrophone();
        } else {
            await this.startMicrophone();
        }
    }

    /**
     * 启动麦克风
     */
    async startMicrophone() {
        try {
            // 请求麦克风权限
            this.microphoneStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });
            
            // 创建音频分析器
            if (!audioEngine.isReady()) {
                await audioEngine.init();
            }
            
            const audioContext = audioEngine.audioContext;
            const source = audioContext.createMediaStreamSource(this.microphoneStream);
            
            this.frequencyAnalyzer = audioContext.createAnalyser();
            this.frequencyAnalyzer.fftSize = 2048;
            this.frequencyAnalyzer.smoothingTimeConstant = 0.8;
            
            source.connect(this.frequencyAnalyzer);
            
            this.isListeningToMicrophone = true;
            
            // 开始频率检测
            this.startFrequencyDetection();
            
            // 更新UI
            const micButton = document.getElementById('microphoneButton');
            if (micButton) {
                micButton.textContent = '🎤 停止';
                micButton.classList.add('active');
            }
            
            console.log('🎤 麦克风已启动');
            
        } catch (error) {
            console.error('❌ 麦克风启动失败:', error);

            // 使用通知系统替代alert弹框
            if (window.notificationSystem) {
                window.notificationSystem.error(
                    '无法访问麦克风，请检查权限设置',
                    { title: '麦克风启动失败', duration: 5000 }
                );
            } else {
                alert('无法访问麦克风，请检查权限设置');
            }
        }
    }

    /**
     * 停止麦克风
     */
    stopMicrophone() {
        if (this.microphoneStream) {
            this.microphoneStream.getTracks().forEach(track => track.stop());
            this.microphoneStream = null;
        }
        
        this.frequencyAnalyzer = null;
        this.isListeningToMicrophone = false;
        
        // 更新UI
        const micButton = document.getElementById('microphoneButton');
        if (micButton) {
            micButton.textContent = '🎤 开始';
            micButton.classList.remove('active');
        }
        
        console.log('🎤 麦克风已停止');
    }

    /**
     * 开始频率检测
     */
    startFrequencyDetection() {
        if (!this.isListeningToMicrophone || !this.frequencyAnalyzer) return;
        
        const bufferLength = this.frequencyAnalyzer.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        
        const detectFrequency = () => {
            if (!this.isListeningToMicrophone) return;
            
            this.frequencyAnalyzer.getByteFrequencyData(dataArray);
            
            // 找到最强的频率
            let maxIndex = 0;
            let maxValue = 0;
            
            for (let i = 0; i < bufferLength; i++) {
                if (dataArray[i] > maxValue) {
                    maxValue = dataArray[i];
                    maxIndex = i;
                }
            }
            
            // 转换为频率
            if (maxValue > 50) { // 阈值过滤噪音
                const sampleRate = audioEngine.audioContext.sampleRate;
                const frequency = (maxIndex * sampleRate) / (bufferLength * 2);
                
                if (frequency >= this.frequencyRange.min && frequency <= this.frequencyRange.max) {
                    this.setFrequency(frequency);
                }
            }
            
            requestAnimationFrame(detectFrequency);
        };
        
        detectFrequency();
    }

    /**
     * 设置频率
     * @param {number} frequency - 频率值
     */
    setFrequency(frequency) {
        frequency = MathUtils.clamp(frequency, this.frequencyRange.min, this.frequencyRange.max);
        
        if (Math.abs(frequency - this.currentFrequency) > 1) {
            this.currentFrequency = frequency;
            
            // 更新UI
            this.updateFrequencyDisplay(frequency);
            
            // 触发回调
            if (this.onFrequencyChange) {
                this.onFrequencyChange(frequency);
            }
        }
    }

    /**
     * 调整频率
     * @param {number} delta - 频率变化量
     */
    adjustFrequency(delta) {
        this.setFrequency(this.currentFrequency + delta);
    }

    /**
     * 更新频率显示
     * @param {number} frequency - 频率值
     */
    updateFrequencyDisplay(frequency) {
        // 更新滑块值 - 使用正确的ID
        const frequencySlider = document.getElementById('frequency-slider');
        if (frequencySlider) {
            frequencySlider.value = frequency;
        }

        // 更新输入框值
        const frequencyInput = document.getElementById('frequencyInput');
        if (frequencyInput) {
            frequencyInput.value = frequency.toFixed(0);
        }

        // 更新显示值 - 使用正确的ID
        const frequencyValue = document.getElementById('frequency-value');
        if (frequencyValue) {
            frequencyValue.textContent = `${frequency.toFixed(0)} Hz`;
        }

        // 兼容旧的显示元素ID
        const frequencyDisplay = document.getElementById('frequencyDisplay');
        if (frequencyDisplay) {
            frequencyDisplay.textContent = `${frequency.toFixed(0)} Hz`;
        }
    }

    /**
     * 切换静音
     */
    toggleMute() {
        if (audioEngine.isReady()) {
            const currentVolume = audioEngine.getVolume('master');
            audioEngine.setVolume('master', currentVolume > 0 ? 0 : 0.8);
        }
    }

    /**
     * 检查按键是否按下
     * @param {string} key - 按键代码
     * @returns {boolean} 是否按下
     */
    isKeyPressed(key) {
        return this.keys.has(key);
    }

    /**
     * 获取当前指针位置
     * @returns {Object} 位置坐标 {x, y}
     */
    getCurrentPointerPosition() {
        return { ...this.lastPointerPosition };
    }

    /**
     * 启用输入
     */
    enable() {
        this.isEnabled = true;
    }

    /**
     * 禁用输入
     */
    disable() {
        this.isEnabled = false;
        this.keys.clear();
        this.pointers.clear();
    }

    /**
     * 更新输入管理器
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        // 更新手势检测
        this.gestureDetector.update(deltaTime);
    }

    /**
     * 销毁输入管理器
     */
    destroy() {
        // 停止麦克风
        this.stopMicrophone();
        
        // 移除事件监听器
        if (this.canvas) {
            this.canvas.removeEventListener('mousedown', this.handlePointerDown);
            this.canvas.removeEventListener('mousemove', this.handlePointerMove);
            this.canvas.removeEventListener('mouseup', this.handlePointerUp);
            this.canvas.removeEventListener('wheel', this.handleWheel);
            this.canvas.removeEventListener('touchstart', this.handleTouchStart);
            this.canvas.removeEventListener('touchmove', this.handleTouchMove);
            this.canvas.removeEventListener('touchend', this.handleTouchEnd);
        }
        
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        
        // 销毁手势检测器
        this.gestureDetector.destroy();
        
        console.log('🎮 输入管理器已销毁');
    }
}

// 导出类到全局作用域
window.GestureDetector = GestureDetector;
window.InputManager = InputManager;

// 创建全局输入管理器实例
window.inputManager = new InputManager();
