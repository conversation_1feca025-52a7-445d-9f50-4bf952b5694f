/**
 * 量子共鸣者 - 教学提示系统
 * 负责游戏的新手引导和教学提示功能
 */

class TutorialSystem {
    constructor() {
        this.isInitialized = false;
        this.isActive = false;
        this.currentStep = 0;
        this.currentTutorial = null;
        
        // 教学步骤数据
        this.tutorialSteps = [];
        
        // UI元素
        this.elements = {
            overlay: null,
            tooltip: null,
            highlight: null,
            arrow: null,
            skipButton: null,
            nextButton: null,
            prevButton: null,
            progressBar: null
        };
        
        // 配置选项
        this.config = {
            showSkipButton: true,
            autoAdvance: false,
            highlightPadding: 10,
            tooltipOffset: 20,
            animationDuration: 300
        };
        
        // 事件回调
        this.callbacks = {
            onStart: null,
            onStep: null,
            onComplete: null,
            onSkip: null
        };
        
        console.log('📚 教学提示系统已创建');
    }

    /**
     * 初始化教学提示系统
     */
    init() {
        try {
            // 创建UI结构
            this.createTutorialUI();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 加载预定义教程
            this.loadPredefinedTutorials();
            
            this.isInitialized = true;
            console.log('✅ 教学提示系统初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 教学提示系统初始化失败:', error);
            return false;
        }
    }

    /**
     * 创建教学UI结构
     */
    createTutorialUI() {
        // 创建遮罩层
        this.elements.overlay = document.createElement('div');
        this.elements.overlay.className = 'tutorial-overlay';
        this.elements.overlay.style.display = 'none';
        
        // 创建提示框
        this.elements.tooltip = document.createElement('div');
        this.elements.tooltip.className = 'tutorial-tooltip';
        
        // 创建高亮区域
        this.elements.highlight = document.createElement('div');
        this.elements.highlight.className = 'tutorial-highlight';
        
        // 创建指示箭头
        this.elements.arrow = document.createElement('div');
        this.elements.arrow.className = 'tutorial-arrow';
        
        // 创建控制按钮
        this.createControlButtons();
        
        // 创建进度条
        this.elements.progressBar = document.createElement('div');
        this.elements.progressBar.className = 'tutorial-progress';
        this.elements.progressBar.innerHTML = `
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <span class="progress-text">1 / 1</span>
        `;
        
        // 组装UI结构
        this.elements.overlay.appendChild(this.elements.highlight);
        this.elements.overlay.appendChild(this.elements.arrow);
        this.elements.overlay.appendChild(this.elements.tooltip);
        this.elements.overlay.appendChild(this.elements.progressBar);
        
        // 添加到页面
        document.body.appendChild(this.elements.overlay);
    }

    /**
     * 创建控制按钮
     */
    createControlButtons() {
        // 跳过按钮
        this.elements.skipButton = document.createElement('button');
        this.elements.skipButton.className = 'tutorial-btn skip-btn';
        this.elements.skipButton.innerHTML = '跳过教程';
        
        // 下一步按钮
        this.elements.nextButton = document.createElement('button');
        this.elements.nextButton.className = 'tutorial-btn next-btn primary';
        this.elements.nextButton.innerHTML = '下一步';
        
        // 上一步按钮
        this.elements.prevButton = document.createElement('button');
        this.elements.prevButton.className = 'tutorial-btn prev-btn';
        this.elements.prevButton.innerHTML = '上一步';
        this.elements.prevButton.style.display = 'none';
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 跳过按钮
        this.elements.skipButton.addEventListener('click', () => {
            this.skipTutorial();
        });
        
        // 下一步按钮
        this.elements.nextButton.addEventListener('click', () => {
            this.nextStep();
        });
        
        // 上一步按钮
        this.elements.prevButton.addEventListener('click', () => {
            this.prevStep();
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (!this.isActive) return;
            
            switch (e.key) {
                case 'Escape':
                    this.skipTutorial();
                    break;
                case 'ArrowRight':
                case 'Space':
                    e.preventDefault();
                    this.nextStep();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.prevStep();
                    break;
            }
        });
        
        // 点击遮罩层外部区域继续
        this.elements.overlay.addEventListener('click', (e) => {
            if (e.target === this.elements.overlay) {
                this.nextStep();
            }
        });
    }

    /**
     * 加载预定义教程
     */
    loadPredefinedTutorials() {
        // 第一关教程步骤
        this.registerTutorial('level1', {
            name: '量子共鸣入门',
            description: '学习基础的量子共鸣操作',
            steps: [
                {
                    title: '欢迎来到量子共鸣者！',
                    content: '在这个游戏中，你将学会操控量子粒子，创造美妙的共鸣效果。让我们开始第一课吧！',
                    target: null,
                    position: 'center',
                    action: 'none'
                },
                {
                    title: '认识游戏界面',
                    content: '这是游戏的主要区域，量子粒子会在这里显示。你可以看到几个发光的粒子正在等待激活。',
                    target: '#game-canvas',
                    position: 'bottom',
                    action: 'highlight'
                },
                {
                    title: '频率控制面板',
                    content: '这是频率控制面板。通过调节频率滑块，你可以改变目标频率来激活不同的粒子。',
                    target: '.frequency-panel',
                    position: 'top',
                    action: 'highlight'
                },
                {
                    title: '点击激活粒子',
                    content: '现在试着点击画面中的任意一个粒子来激活它。当粒子的频率与你设定的目标频率接近时，它就会被激活！',
                    target: '#game-canvas',
                    position: 'top',
                    action: 'wait_for_click',
                    waitFor: 'particle_activated'
                },
                {
                    title: '观察共鸣效果',
                    content: '太棒了！你成功激活了一个粒子。注意观察粒子之间的连接线，当多个粒子被激活时会产生共鸣效果。',
                    target: '#game-canvas',
                    position: 'bottom',
                    action: 'highlight'
                },
                {
                    title: '查看得分和目标',
                    content: '你的得分会显示在这里。完成关卡需要达到目标分数并满足特定条件。',
                    target: '.score-info',
                    position: 'bottom',
                    action: 'highlight'
                },
                {
                    title: '继续探索',
                    content: '现在你已经掌握了基础操作！继续激活更多粒子，创造连锁反应，完成这个关卡吧！',
                    target: null,
                    position: 'center',
                    action: 'none'
                }
            ]
        });
    }

    /**
     * 注册教程
     * @param {string} id - 教程ID
     * @param {Object} tutorial - 教程数据
     */
    registerTutorial(id, tutorial) {
        if (!this.tutorials) {
            this.tutorials = new Map();
        }
        this.tutorials.set(id, tutorial);
        console.log(`📚 已注册教程: ${tutorial.name}`);
    }

    /**
     * 开始教程
     * @param {string} tutorialId - 教程ID
     * @param {Object} options - 选项
     */
    startTutorial(tutorialId, options = {}) {
        if (!this.isInitialized) {
            console.warn('⚠️ 教学提示系统未初始化');
            return false;
        }

        const tutorial = this.tutorials?.get(tutorialId);
        if (!tutorial) {
            console.error(`❌ 未找到教程: ${tutorialId}`);
            return false;
        }

        this.currentTutorial = tutorial;
        this.tutorialSteps = tutorial.steps;
        this.currentStep = 0;
        this.isActive = true;

        // 应用选项
        Object.assign(this.config, options);

        // 显示教程界面
        this.showTutorial();

        // 显示第一步
        this.showStep(0);

        // 触发开始回调
        if (this.callbacks.onStart) {
            this.callbacks.onStart(tutorial);
        }

        console.log(`📚 开始教程: ${tutorial.name}`);
        return true;
    }
}
